'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import UpvoteButton from '@/components/common/UpvoteButton';
import { Button } from '@/components/ui/button';

// Utility function to generate a valid UUID v4
const generateTestUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

/**
 * Test component for upvote functionality
 * This component can be temporarily added to any page to test upvote features
 */
const UpvoteTestComponent: React.FC = () => {
  const { 
    session, 
    upvotedEntityIds, 
    isLoadingUpvotedIds, 
    fetchUpvotedIds,
    handleUpvote,
    handleRemoveUpvote 
  } = useAuth();

  const [testEntityId] = useState(() => generateTestUUID()); // Generate a valid UUID for testing
  const [testUpvoteCount, setTestUpvoteCount] = useState(42);
  const [apiTestResult, setApiTestResult] = useState<string>('');

  const testDirectApiCall = async () => {
    if (!session?.access_token) {
      setApiTestResult('❌ No session token available');
      return;
    }

    try {
      setApiTestResult('🔄 Testing direct API call...');
      console.log(`[Test] Testing upvote for entity: ${testEntityId}`);
      console.log(`[Test] Session token exists: ${!!session.access_token}`);
      console.log(`[Test] API Base URL: ${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'}`);

      // Test adding upvote
      await handleUpvote(testEntityId);
      setApiTestResult('✅ Direct upvote API call successful');

      // Test removing upvote after a short delay
      setTimeout(async () => {
        try {
          await handleRemoveUpvote(testEntityId);
          setApiTestResult('✅ Direct upvote removal API call successful');
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          setApiTestResult(`❌ Direct removal API call failed: ${errorMessage}`);
          console.error('Direct removal API test error:', error);
        }
      }, 1000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setApiTestResult(`❌ Direct API call failed: ${errorMessage}`);
      console.error('Direct API test error:', error);
    }
  };

  const testFetchUpvotedIds = async () => {
    try {
      setApiTestResult('🔄 Testing fetch upvoted IDs...');
      await fetchUpvotedIds();
      setApiTestResult('✅ Fetch upvoted IDs successful');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setApiTestResult(`❌ Fetch upvoted IDs failed: ${errorMessage}`);
      console.error('Fetch upvoted IDs test error:', error);
    }
  };

  return (
    <div className="p-6 bg-gray-50 border border-gray-200 rounded-lg max-w-2xl mx-auto my-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">🧪 Upvote System Test</h2>
      
      {/* Authentication Status */}
      <div className="mb-6 p-4 bg-white rounded-lg border">
        <h3 className="text-lg font-semibold mb-2">Authentication Status</h3>
        <p className="text-sm">
          <strong>Logged in:</strong> {session ? '✅ Yes' : '❌ No'}
        </p>
        {session && (
          <p className="text-sm">
            <strong>User ID:</strong> {session.user?.id}
          </p>
        )}
        <p className="text-sm">
          <strong>API Base URL:</strong> <code className="text-xs bg-gray-100 px-1 rounded">
            {process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'}
          </code>
        </p>
      </div>

      {/* Upvoted IDs Status */}
      <div className="mb-6 p-4 bg-white rounded-lg border">
        <h3 className="text-lg font-semibold mb-2">Upvoted IDs Status</h3>
        <p className="text-sm">
          <strong>Loading:</strong> {isLoadingUpvotedIds ? '🔄 Yes' : '✅ No'}
        </p>
        <p className="text-sm">
          <strong>Count:</strong> {upvotedEntityIds.size}
        </p>
        <p className="text-sm">
          <strong>Test Entity ID:</strong> <code className="text-xs bg-gray-100 px-1 rounded">{testEntityId}</code>
        </p>
        <p className="text-sm">
          <strong>Test Entity Upvoted:</strong> {upvotedEntityIds.has(testEntityId) ? '✅ Yes' : '❌ No'}
        </p>
        {upvotedEntityIds.size > 0 && (
          <details className="mt-2">
            <summary className="text-sm font-medium cursor-pointer">View All Upvoted IDs</summary>
            <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
              {JSON.stringify(Array.from(upvotedEntityIds), null, 2)}
            </pre>
          </details>
        )}
      </div>

      {/* Test Buttons */}
      <div className="mb-6 space-y-3">
        <h3 className="text-lg font-semibold">Test Actions</h3>
        <div className="flex flex-wrap gap-3">
          <Button onClick={testFetchUpvotedIds} variant="outline">
            🔄 Refresh Upvoted IDs
          </Button>
          <Button onClick={testDirectApiCall} variant="outline" disabled={!session}>
            🧪 Test Direct API Calls
          </Button>
        </div>
      </div>

      {/* API Test Results */}
      {apiTestResult && (
        <div className="mb-6 p-4 bg-white rounded-lg border">
          <h3 className="text-lg font-semibold mb-2">API Test Results</h3>
          <p className="text-sm font-mono">{apiTestResult}</p>
        </div>
      )}

      {/* UpvoteButton Component Tests */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">UpvoteButton Component Tests</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Compact Variant */}
          <div className="p-4 bg-white rounded-lg border">
            <h4 className="font-medium mb-3">Compact Variant (Small)</h4>
            <UpvoteButton
              entityId={testEntityId}
              initialUpvoteCount={testUpvoteCount}
              isInitiallyUpvoted={false}
              size="sm"
              variant="compact"
            />
          </div>

          {/* Default Variant */}
          <div className="p-4 bg-white rounded-lg border">
            <h4 className="font-medium mb-3">Default Variant (Medium)</h4>
            <UpvoteButton
              entityId={testEntityId}
              initialUpvoteCount={testUpvoteCount}
              isInitiallyUpvoted={false}
              size="md"
              variant="default"
            />
          </div>

          {/* Large Variant */}
          <div className="p-4 bg-white rounded-lg border">
            <h4 className="font-medium mb-3">Large Variant</h4>
            <UpvoteButton
              entityId={testEntityId}
              initialUpvoteCount={testUpvoteCount}
              isInitiallyUpvoted={false}
              size="lg"
              variant="compact"
            />
          </div>

          {/* Different Entity */}
          <div className="p-4 bg-white rounded-lg border">
            <h4 className="font-medium mb-3">Different Entity</h4>
            <UpvoteButton
              entityId={generateTestUUID()}
              initialUpvoteCount={15}
              isInitiallyUpvoted={false}
              size="md"
              variant="compact"
            />
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">Test Instructions</h3>
        <ol className="text-sm text-blue-800 space-y-1">
          <li>1. Make sure you're logged in to test authenticated features</li>
          <li>2. Click "Refresh Upvoted IDs" to test the GET /users/me/upvoted-ids endpoint</li>
          <li>3. Click "Test Direct API Calls" to test POST/DELETE upvote endpoints</li>
          <li>4. Try clicking the UpvoteButton components to test UI interactions</li>
          <li>5. Check browser console for detailed logs</li>
        </ol>
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> The test uses generated UUIDs that may not exist in the database.
            For testing with real entities, visit the <a href="/browse" className="underline">browse page</a>
            and test upvoting actual entities.
          </p>
        </div>
      </div>
    </div>
  );
};

export default UpvoteTestComponent;
